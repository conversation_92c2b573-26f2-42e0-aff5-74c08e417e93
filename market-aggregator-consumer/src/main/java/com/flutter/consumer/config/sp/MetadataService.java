package com.flutter.consumer.config.sp;

import com.ppb.platform.stream.metadata.model.PersistenceMetadataInfo;
import com.ppb.platform.stream.metadata.serialization.PersistenceMetadataInfoProtoSerDe;
import com.ppb.platform.stream.metadata.serialization.PersistenceMetadataInfoSerDe;
import com.ppb.platform.stream.metadata.service.ZookeeperMetadataService;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.apache.zookeeper.CreateMode;
import scala.collection.JavaConversions;
import java.util.Collections;

public class MetadataService {

//    private final ZookeeperMetadataService zookeeperMetadataService;
//    private final PersistenceMetadataInfoProtoSerDe persistenceMetadataInfoProtoSerDe;

    public MetadataService() {
//        this.zookeeperMetadataService = zookeeperMetadataService;
//        this.persistenceMetadataInfoProtoSerDe = persistenceMetadataInfoProtoSerDe;
    }

    public void start() throws Exception {
        PersistenceMetadataInfoSerDe persistenceMetadataInfoSerDe = new PersistenceMetadataInfoProtoSerDe();
        CuratorFramework client = CuratorFrameworkFactory.newClient("localhost:2181", new ExponentialBackoffRetry(1000, 3));
        client.start();

        for (int partition = 0; partition < 3; partition++) {
            var path = "/test/streamprotocol/metadata/" + partition;
            if (client.checkExists().forPath(path) != null) {
                continue;
            }
            client.create().creatingParentsIfNeeded().withMode(CreateMode.PERSISTENT).forPath(path);
            PersistenceMetadataInfo persistentMetadataInfo = new PersistenceMetadataInfo("test", partition, 0, JavaConversions.asScalaBuffer(Collections.emptyList()).toList());
            client.setData().forPath(path, persistenceMetadataInfoSerDe.serialize(persistentMetadataInfo));


        }
    }

}
